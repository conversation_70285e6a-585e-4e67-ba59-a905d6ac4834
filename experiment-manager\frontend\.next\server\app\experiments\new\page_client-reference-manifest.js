globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/experiments/new/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/app-layout.tsx":{"*":{"id":"(ssr)/./src/components/layout/app-layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/breadcrumb.tsx":{"*":{"id":"(ssr)/./src/components/layout/breadcrumb.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/experiments/page.tsx":{"*":{"id":"(ssr)/./src/app/experiments/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/experiments/new/page.tsx":{"*":{"id":"(ssr)/./src/app/experiments/new/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/mnt/e/01-main/framework/experiment-manager/frontend/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/e/01-main/framework/experiment-manager/frontend/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/e/01-main/framework/experiment-manager/frontend/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/e/01-main/framework/experiment-manager/frontend/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/e/01-main/framework/experiment-manager/frontend/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/e/01-main/framework/experiment-manager/frontend/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/e/01-main/framework/experiment-manager/frontend/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/e/01-main/framework/experiment-manager/frontend/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/e/01-main/framework/experiment-manager/frontend/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/e/01-main/framework/experiment-manager/frontend/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/e/01-main/framework/experiment-manager/frontend/node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/e/01-main/framework/experiment-manager/frontend/node_modules/next/dist/esm/client/components/static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/mnt/e/01-main/framework/experiment-manager/frontend/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx":{"id":"(app-pages-browser)/./src/components/layout/app-layout.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx":{"id":"(app-pages-browser)/./src/components/layout/breadcrumb.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/page.tsx":{"id":"(app-pages-browser)/./src/app/experiments/page.tsx","name":"*","chunks":["app/experiments/page","static/chunks/app/experiments/page.js"],"async":false},"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx":{"id":"(app-pages-browser)/./src/app/experiments/new/page.tsx","name":"*","chunks":["app/experiments/new/page","static/chunks/app/experiments/new/page.js"],"async":false}},"entryCSSFiles":{"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page":[],"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/layout":["static/css/app/layout.css"],"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/page":[],"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page":[]}}
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2Fapp-layout.tsx&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2Fbreadcrumb.tsx&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2Fapp-layout.tsx&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2Fbreadcrumb.tsx&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/app-layout.tsx */ \"(ssr)/./src/components/layout/app-layout.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/breadcrumb.tsx */ \"(ssr)/./src/components/layout/breadcrumb.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGbW50JTJGZSUyRjAxLW1haW4lMkZmcmFtZXdvcmslMkZleHBlcmltZW50LW1hbmFnZXIlMkZmcm9udGVuZCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlMkZhcHAlMkZsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPSUyRm1udCUyRmUlMkYwMS1tYWluJTJGZnJhbWV3b3JrJTJGZXhwZXJpbWVudC1tYW5hZ2VyJTJGZnJvbnRlbmQlMkZzcmMlMkZhcHAlMkZnbG9iYWxzLmNzcyZtb2R1bGVzPSUyRm1udCUyRmUlMkYwMS1tYWluJTJGZnJhbWV3b3JrJTJGZXhwZXJpbWVudC1tYW5hZ2VyJTJGZnJvbnRlbmQlMkZzcmMlMkZjb21wb25lbnRzJTJGbGF5b3V0JTJGYXBwLWxheW91dC50c3gmbW9kdWxlcz0lMkZtbnQlMkZlJTJGMDEtbWFpbiUyRmZyYW1ld29yayUyRmV4cGVyaW1lbnQtbWFuYWdlciUyRmZyb250ZW5kJTJGc3JjJTJGY29tcG9uZW50cyUyRmxheW91dCUyRmJyZWFkY3J1bWIudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3TEFBOEg7QUFDOUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9leHBlcmltZW50LW1hbmFnZXItZnJvbnRlbmQvP2VlNGQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvbW50L2UvMDEtbWFpbi9mcmFtZXdvcmsvZXhwZXJpbWVudC1tYW5hZ2VyL2Zyb250ZW5kL3NyYy9jb21wb25lbnRzL2xheW91dC9hcHAtbGF5b3V0LnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL21udC9lLzAxLW1haW4vZnJhbWV3b3JrL2V4cGVyaW1lbnQtbWFuYWdlci9mcm9udGVuZC9zcmMvY29tcG9uZW50cy9sYXlvdXQvYnJlYWRjcnVtYi50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2Fapp-layout.tsx&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2Fbreadcrumb.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGbW50JTJGZSUyRjAxLW1haW4lMkZmcmFtZXdvcmslMkZleHBlcmltZW50LW1hbmFnZXIlMkZmcm9udGVuZCUyRnNyYyUyRmFwcCUyRnBhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2V4cGVyaW1lbnQtbWFuYWdlci1mcm9udGVuZC8/ZmJiYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9tbnQvZS8wMS1tYWluL2ZyYW1ld29yay9leHBlcmltZW50LW1hbmFnZXIvZnJvbnRlbmQvc3JjL2FwcC9wYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Clock,FlaskConical,Plus,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Clock,FlaskConical,Plus,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Clock,FlaskConical,Plus,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/flask-conical.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Clock,FlaskConical,Plus,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Clock,FlaskConical,Plus,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Clock,FlaskConical,Plus,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Clock,FlaskConical,Plus,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Clock,FlaskConical,Plus,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Clock,FlaskConical,Plus,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/**\n * 现代化实验管理系统首页\n * 展示系统概览和快速操作\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-hidden rounded-2xl bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 p-8 text-center text-white shadow-2xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold mb-4\",\n                                children: \"\\uD83C\\uDF89 现代化实验管理系统\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl opacity-90\",\n                                children: \"全新的现代化界面已激活，享受更优雅的科研管理体验\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center flex flex-col gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative inline-block self-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur-2xl opacity-20\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"relative text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                children: \"实验管理系统\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n                        children: '基于\"实验即契约\"理念的现代化科研管理平台，让每个实验都可追溯、可复现'\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row items-center justify-center gap-4 pt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/experiments/new\",\n                                className: \"group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex items-center gap-2 hover:scale-105 hover:-translate-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"创建新实验\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/experiments\",\n                                className: \"group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-white text-gray-700 px-8 py-3 text-lg font-semibold rounded-xl border-2 border-gray-300 hover:border-blue-500 hover:shadow-lg transition-all duration-300 flex items-center gap-2 hover:scale-105 hover:-translate-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"查看所有实验\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer hover:-translate-y-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-600 text-sm font-medium mb-2\",\n                                            children: \"总实验数\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-blue-900\",\n                                            children: \"24\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-8 w-8 text-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 border border-green-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer hover:-translate-y-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-green-600 text-sm font-medium mb-2\",\n                                            children: \"已完成\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-green-900\",\n                                            children: \"18\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-8 w-8 text-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-6 border border-orange-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer hover:-translate-y-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-orange-600 text-sm font-medium mb-2\",\n                                            children: \"进行中\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-orange-900\",\n                                            children: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-8 w-8 text-orange-500\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 border border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer hover:-translate-y-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-purple-600 text-sm font-medium mb-2\",\n                                            children: \"成功率\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-purple-900\",\n                                            children: \"92%\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-8 w-8 text-purple-500\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/80 backdrop-blur-lg rounded-3xl p-8 border border-gray-200/50 shadow-2xl hover:shadow-3xl transition-all duration-300\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                            children: \"核心功能\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-4 p-4 bg-blue-50/50 hover:bg-blue-50/80 rounded-xl transition-colors duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 mt-1.5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-gray-900 mb-1\",\n                                                            children: \"实验全生命周期管理\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm\",\n                                                            children: \"从假设到结论的完整追踪，确保每个实验都可追溯\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-4 p-4 bg-purple-50/50 hover:bg-purple-50/80 rounded-xl transition-colors duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 mt-1.5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-gray-900 mb-1\",\n                                                            children: \"智能复盘系统\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm\",\n                                                            children: \"三阶段十二步科学复盘流程，深度挖掘实验价值\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-4 p-4 bg-green-50/50 hover:bg-green-50/80 rounded-xl transition-colors duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 rounded-full bg-gradient-to-r from-green-500 to-green-600 mt-1.5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-gray-900 mb-1\",\n                                                            children: \"数据可视化分析\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm\",\n                                                            children: \"直观的图表和趋势分析，让数据说话\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/80 backdrop-blur-lg rounded-3xl p-8 border border-gray-200/50 shadow-2xl hover:shadow-3xl transition-all duration-300\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent\",\n                                            children: \"最近活动\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 p-5 bg-green-50/80 hover:bg-green-50 rounded-xl border border-green-200/50 hover:shadow-md transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 bg-green-500 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-5 w-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-semibold text-gray-900 mb-1\",\n                                                            children: \"深度学习模型训练\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-green-600 font-medium\",\n                                                            children: \"2小时前完成\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 p-5 bg-blue-50/80 hover:bg-blue-50 rounded-xl border border-blue-200/50 hover:shadow-md transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 bg-blue-500 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-5 w-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-semibold text-gray-900 mb-1\",\n                                                            children: \"数据预处理实验\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-blue-600 font-medium\",\n                                                            children: \"正在进行中\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 p-5 bg-orange-50/80 hover:bg-orange-50 rounded-xl border border-orange-200/50 hover:shadow-md transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 bg-orange-500 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Clock_FlaskConical_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-semibold text-gray-900 mb-1\",\n                                                            children: \"模型评估分析\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-orange-600 font-medium\",\n                                                            children: \"等待开始\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/app-layout.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/app-layout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppLayout: () => (/* binding */ AppLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FlaskConical,Menu,Plus,Settings,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FlaskConical,Menu,Plus,Settings,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/flask-conical.js\");\n/* harmony import */ var _barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FlaskConical,Menu,Plus,Settings,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FlaskConical,Menu,Plus,Settings,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FlaskConical,Menu,Plus,Settings,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FlaskConical,Menu,Plus,Settings,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/**\n * 实验管理系统布局组件 - 与现有页面风格完全一致\n * 采用内联样式，玻璃态效果，与 experiments 页面设计语言统一\n */ /* __next_internal_client_entry_do_not_use__ AppLayout auto */ \n\n\n\n\n// 导航菜单配置 - 与现有页面风格一致\nconst navigationItems = [\n    {\n        name: \"仪表板\",\n        href: \"/\",\n        icon: _barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"#2563eb\"\n    },\n    {\n        name: \"实验管理\",\n        href: \"/experiments\",\n        icon: _barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        color: \"#8b5cf6\"\n    },\n    {\n        name: \"创建实验\",\n        href: \"/experiments/new\",\n        icon: _barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: \"#059669\"\n    },\n    {\n        name: \"系统设置\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        color: \"#d97706\"\n    }\n];\nfunction AppLayout({ children }) {\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // 检测当前页面类型\n    const isExperimentDetailPage = pathname.match(/^\\/experiments\\/[^\\/]+$/);\n    const isExperimentSubPage = pathname.match(/^\\/experiments\\/[^\\/]+\\//);\n    const experimentId = pathname.match(/^\\/experiments\\/([^\\/]+)/)?.[1];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: \"100vh\",\n            background: \"linear-gradient(135deg, #f8fafc 0%, rgba(219, 234, 254, 0.3) 50%, rgba(224, 231, 255, 0.2) 100%)\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    zIndex: 50,\n                    background: \"rgba(255, 255, 255, 0.9)\",\n                    backdropFilter: \"blur(12px)\",\n                    borderBottom: \"1px solid rgba(229, 231, 235, 0.5)\",\n                    boxShadow: \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: \"1400px\",\n                        margin: \"0 auto\",\n                        padding: \"0 1rem\",\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        height: \"4rem\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"0.75rem\",\n                                    textDecoration: \"none\",\n                                    transition: \"all 0.3s ease\"\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.transform = \"scale(1.02)\";\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.transform = \"scale(1)\";\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"0.75rem\",\n                                            background: \"linear-gradient(to right, #3b82f6, #8b5cf6)\",\n                                            borderRadius: \"0.75rem\",\n                                            boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            style: {\n                                                height: \"1.5rem\",\n                                                width: \"1.5rem\",\n                                                color: \"white\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            flexDirection: \"column\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"1.25rem\",\n                                                    fontWeight: \"bold\",\n                                                    background: \"linear-gradient(to right, #2563eb, #8b5cf6)\",\n                                                    WebkitBackgroundClip: \"text\",\n                                                    backgroundClip: \"text\",\n                                                    WebkitTextFillColor: \"transparent\",\n                                                    color: \"transparent\"\n                                                },\n                                                children: \"实验管理系统\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"0.75rem\",\n                                                    color: \"#6b7280\"\n                                                },\n                                                children: \"科研实验管理平台\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"0.5rem\"\n                            },\n                            children: [\n                                (isExperimentDetailPage || isExperimentSubPage) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: \"0.5rem\",\n                                        marginRight: \"1rem\",\n                                        padding: \"0.5rem 0.75rem\",\n                                        background: \"rgba(255, 255, 255, 0.6)\",\n                                        borderRadius: \"0.5rem\",\n                                        fontSize: \"0.875rem\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/experiments\",\n                                            style: {\n                                                color: \"#6b7280\",\n                                                textDecoration: \"none\",\n                                                transition: \"color 0.3s ease\"\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.currentTarget.style.color = \"#374151\";\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.currentTarget.style.color = \"#6b7280\";\n                                            },\n                                            children: \"实验管理\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: \"#d1d5db\"\n                                            },\n                                            children: \"/\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: \"#374151\",\n                                                fontWeight: \"500\"\n                                            },\n                                            children: [\n                                                \"实验 #\",\n                                                experimentId\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this),\n                                navigationItems.map((item)=>{\n                                    const Icon = item.icon;\n                                    const isActive = pathname === item.href || item.href !== \"/\" && pathname.startsWith(item.href);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"0.5rem\",\n                                            padding: \"0.75rem 1rem\",\n                                            borderRadius: \"0.75rem\",\n                                            fontSize: \"0.875rem\",\n                                            fontWeight: \"600\",\n                                            textDecoration: \"none\",\n                                            transition: \"all 0.3s ease\",\n                                            background: isActive ? \"linear-gradient(to right, #2563eb, #8b5cf6)\" : \"transparent\",\n                                            color: isActive ? \"white\" : \"#374151\",\n                                            boxShadow: isActive ? \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\" : \"none\",\n                                            transform: isActive ? \"scale(1.05)\" : \"scale(1)\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            if (!isActive) {\n                                                e.currentTarget.style.background = \"rgba(255, 255, 255, 0.7)\";\n                                                e.currentTarget.style.boxShadow = \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\";\n                                                e.currentTarget.style.transform = \"scale(1.02)\";\n                                            }\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            if (!isActive) {\n                                                e.currentTarget.style.background = \"transparent\";\n                                                e.currentTarget.style.boxShadow = \"none\";\n                                                e.currentTarget.style.transform = \"scale(1)\";\n                                            }\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                style: {\n                                                    height: \"1rem\",\n                                                    width: \"1rem\",\n                                                    color: isActive ? \"white\" : item.color\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"0.75rem\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\",\n                                    background: \"rgba(255, 255, 255, 0.7)\",\n                                    border: \"1px solid rgba(229, 231, 235, 0.5)\",\n                                    borderRadius: \"0.5rem\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.3s ease\"\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.background = \"rgba(243, 244, 246, 0.8)\";\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.background = \"rgba(255, 255, 255, 0.7)\";\n                                },\n                                className: \"md:hidden\",\n                                children: mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    style: {\n                                        height: \"1.25rem\",\n                                        width: \"1.25rem\",\n                                        color: \"#6b7280\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    style: {\n                                        height: \"1.25rem\",\n                                        width: \"1.25rem\",\n                                        color: \"#6b7280\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"fixed\",\n                    top: \"4rem\",\n                    left: 0,\n                    right: 0,\n                    zIndex: 40,\n                    background: \"rgba(255, 255, 255, 0.9)\",\n                    backdropFilter: \"blur(12px)\",\n                    borderBottom: \"1px solid rgba(229, 231, 235, 0.5)\",\n                    padding: \"1rem\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        gap: \"0.5rem\"\n                    },\n                    children: [\n                        (isExperimentDetailPage || isExperimentSubPage) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"0.5rem\",\n                                padding: \"0.75rem\",\n                                background: \"rgba(239, 246, 255, 0.5)\",\n                                borderRadius: \"0.5rem\",\n                                marginBottom: \"0.75rem\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FlaskConical_Menu_Plus_Settings_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    style: {\n                                        height: \"1rem\",\n                                        width: \"1rem\",\n                                        color: \"#2563eb\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        fontSize: \"0.875rem\",\n                                        color: \"#2563eb\",\n                                        fontWeight: \"500\"\n                                    },\n                                    children: [\n                                        \"实验 #\",\n                                        experimentId\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 15\n                        }, this),\n                        navigationItems.map((item)=>{\n                            const Icon = item.icon;\n                            const isActive = pathname === item.href || item.href !== \"/\" && pathname.startsWith(item.href);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: item.href,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"0.75rem\",\n                                    padding: \"0.75rem 1rem\",\n                                    borderRadius: \"0.75rem\",\n                                    fontSize: \"0.875rem\",\n                                    fontWeight: \"500\",\n                                    textDecoration: \"none\",\n                                    transition: \"all 0.3s ease\",\n                                    background: isActive ? \"linear-gradient(to right, #2563eb, #8b5cf6)\" : \"transparent\",\n                                    color: isActive ? \"white\" : \"#6b7280\",\n                                    boxShadow: isActive ? \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\" : \"none\"\n                                },\n                                onClick: ()=>setMobileMenuOpen(false),\n                                onMouseEnter: (e)=>{\n                                    if (!isActive) {\n                                        e.currentTarget.style.background = \"rgba(255, 255, 255, 0.5)\";\n                                    }\n                                },\n                                onMouseLeave: (e)=>{\n                                    if (!isActive) {\n                                        e.currentTarget.style.background = \"transparent\";\n                                    }\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        style: {\n                                            height: \"1.25rem\",\n                                            width: \"1.25rem\",\n                                            color: isActive ? \"white\" : item.color\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, item.name, true, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                lineNumber: 256,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    paddingTop: \"4rem\",\n                    minHeight: \"100vh\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: \"1400px\",\n                        margin: \"0 auto\",\n                        padding: \"2rem\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/app-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/breadcrumb.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/breadcrumb.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Breadcrumb: () => (/* binding */ Breadcrumb),\n/* harmony export */   PageHeader: () => (/* binding */ PageHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,Home!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,Home!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/**\n * 面包屑导航组件\n * 提供页面层级导航，帮助用户了解当前位置\n */ /* __next_internal_client_entry_do_not_use__ Breadcrumb,PageHeader auto */ \n\n\n\n\n\n// 根据路径自动生成面包屑的映射\nconst pathToBreadcrumb = {\n    \"/\": [\n        {\n            label: \"首页\",\n            current: true\n        }\n    ],\n    \"/experiments\": [\n        {\n            label: \"首页\",\n            href: \"/\"\n        },\n        {\n            label: \"实验列表\",\n            current: true\n        }\n    ],\n    \"/experiments/new\": [\n        {\n            label: \"首页\",\n            href: \"/\"\n        },\n        {\n            label: \"实验列表\",\n            href: \"/experiments\"\n        },\n        {\n            label: \"创建实验\",\n            current: true\n        }\n    ],\n    \"/settings\": [\n        {\n            label: \"首页\",\n            href: \"/\"\n        },\n        {\n            label: \"设置\",\n            current: true\n        }\n    ]\n};\n// 动态路径处理函数\nfunction generateBreadcrumbFromPath(pathname) {\n    // 检查是否是实验详情页面\n    const experimentDetailMatch = pathname.match(/^\\/experiments\\/([^\\/]+)$/);\n    if (experimentDetailMatch) {\n        const experimentId = experimentDetailMatch[1];\n        return [\n            {\n                label: \"首页\",\n                href: \"/\"\n            },\n            {\n                label: \"实验列表\",\n                href: \"/experiments\"\n            },\n            {\n                label: `实验 ${experimentId.slice(0, 8)}...`,\n                current: true\n            }\n        ];\n    }\n    // 检查是否是实验复盘页面\n    const experimentReviewMatch = pathname.match(/^\\/experiments\\/([^\\/]+)\\/review$/);\n    if (experimentReviewMatch) {\n        const experimentId = experimentReviewMatch[1];\n        return [\n            {\n                label: \"首页\",\n                href: \"/\"\n            },\n            {\n                label: \"实验列表\",\n                href: \"/experiments\"\n            },\n            {\n                label: `实验 ${experimentId.slice(0, 8)}...`,\n                href: `/experiments/${experimentId}`\n            },\n            {\n                label: \"复盘\",\n                current: true\n            }\n        ];\n    }\n    // 检查是否是实验编辑页面\n    const experimentEditMatch = pathname.match(/^\\/experiments\\/([^\\/]+)\\/edit$/);\n    if (experimentEditMatch) {\n        const experimentId = experimentEditMatch[1];\n        return [\n            {\n                label: \"首页\",\n                href: \"/\"\n            },\n            {\n                label: \"实验列表\",\n                href: \"/experiments\"\n            },\n            {\n                label: `实验 ${experimentId.slice(0, 8)}...`,\n                href: `/experiments/${experimentId}`\n            },\n            {\n                label: \"编辑\",\n                current: true\n            }\n        ];\n    }\n    // 返回静态映射或默认面包屑\n    return pathToBreadcrumb[pathname] || [\n        {\n            label: \"首页\",\n            href: \"/\"\n        },\n        {\n            label: \"未知页面\",\n            current: true\n        }\n    ];\n}\nfunction Breadcrumb({ items, className }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // 使用传入的 items 或根据路径自动生成\n    const breadcrumbItems = items || generateBreadcrumbFromPath(pathname);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex items-center space-x-1 text-sm text-gray-500\", className),\n        \"aria-label\": \"面包屑导航\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            breadcrumbItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                    children: [\n                        index > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-4 w-4 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this),\n                        item.current ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-gray-900\",\n                            \"aria-current\": \"page\",\n                            children: item.label\n                        }, void 0, false, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: item.href || \"#\",\n                            className: \"hover:text-gray-700 transition-colors\",\n                            children: item.label\n                        }, void 0, false, {\n                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, index, true, {\n                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\nfunction PageHeader({ title, description, action, breadcrumbItems, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"mb-6\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Breadcrumb, {\n                items: breadcrumbItems,\n                className: \"mb-2\"\n            }, void 0, false, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-600\",\n                                children: description\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: action\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx\",\n        lineNumber: 146,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/breadcrumb.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   capitalize: () => (/* binding */ capitalize),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   deepClone: () => (/* binding */ deepClone),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   isEmpty: () => (/* binding */ isEmpty),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncate: () => (/* binding */ truncate)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n/**\n * 工具函数\n * 提供常用的工具函数，包括类名合并、格式化等\n */ \n\n/**\n * 合并 Tailwind CSS 类名\n * 使用 clsx 和 tailwind-merge 来智能合并类名\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * 格式化文件大小\n */ function formatFileSize(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\n/**\n * 生成随机ID\n */ function generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n/**\n * 防抖函数\n */ function debounce(func, wait) {\n    let timeout = null;\n    return (...args)=>{\n        if (timeout) clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * 节流函数\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\n * 深拷贝对象\n */ function deepClone(obj) {\n    if (obj === null || typeof obj !== \"object\") return obj;\n    if (obj instanceof Date) return new Date(obj.getTime());\n    if (obj instanceof Array) return obj.map((item)=>deepClone(item));\n    if (typeof obj === \"object\") {\n        const clonedObj = {};\n        for(const key in obj){\n            if (obj.hasOwnProperty(key)) {\n                clonedObj[key] = deepClone(obj[key]);\n            }\n        }\n        return clonedObj;\n    }\n    return obj;\n}\n/**\n * 检查是否为空值\n */ function isEmpty(value) {\n    if (value == null) return true;\n    if (typeof value === \"string\") return value.trim().length === 0;\n    if (Array.isArray(value)) return value.length === 0;\n    if (typeof value === \"object\") return Object.keys(value).length === 0;\n    return false;\n}\n/**\n * 首字母大写\n */ function capitalize(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n}\n/**\n * 截断文本\n */ function truncate(str, length) {\n    if (str.length <= length) return str;\n    return str.slice(0, length) + \"...\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"347bc58f1c86\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhwZXJpbWVudC1tYW5hZ2VyLWZyb250ZW5kLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz8xMjczIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMzQ3YmM1OGYxYzg2XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/layout */ \"(rsc)/./src/components/layout/index.ts\");\n\n\n\n\nconst metadata = {\n    title: \"实验管理系统\",\n    description: '基于\"实验即契约\"理念的科研实验管理平台'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout__WEBPACK_IMPORTED_MODULE_2__.AppLayout, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGdCO0FBQzBCO0FBSXpDLE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdWLCtKQUFlO3NCQUM5Qiw0RUFBQ0MseURBQVNBOzBCQUNQSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhwZXJpbWVudC1tYW5hZ2VyLWZyb250ZW5kLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5pbXBvcnQgeyBBcHBMYXlvdXQgfSBmcm9tICcuLi9jb21wb25lbnRzL2xheW91dCdcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSB9KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ+WunumqjOeuoeeQhuezu+e7nycsXG4gIGRlc2NyaXB0aW9uOiAn5Z+65LqOXCLlrp7pqozljbPlpZHnuqZcIueQhuW/teeahOenkeeglOWunumqjOeuoeeQhuW5s+WPsCcsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8QXBwTGF5b3V0PlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9BcHBMYXlvdXQ+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJBcHBMYXlvdXQiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $$typeof: () => (/* binding */ $$typeof),\n/* harmony export */   __esModule: () => (/* binding */ __esModule),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\nconst proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/01-main/framework/experiment-manager/frontend/src/app/page.tsx`)\n\n// Accessing the __esModule property and exporting $$typeof are required here.\n// The __esModule getter forces the proxy target to create the default export\n// and the $$typeof value is for rendering logic to determine if the module\n// is a client boundary.\nconst { __esModule, $$typeof } = proxy;\nconst __default__ = proxy.default;\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/app-layout.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/app-layout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppLayout: () => (/* binding */ e0)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\nconst proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx`)\n\n// Accessing the __esModule property and exporting $$typeof are required here.\n// The __esModule getter forces the proxy target to create the default export\n// and the $$typeof value is for rendering logic to determine if the module\n// is a client boundary.\nconst { __esModule, $$typeof } = proxy;\nconst __default__ = proxy.default;\n\nconst e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/app-layout.tsx#AppLayout`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/app-layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/breadcrumb.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/breadcrumb.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Breadcrumb: () => (/* binding */ e0),\n/* harmony export */   PageHeader: () => (/* binding */ e1)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\nconst proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx`)\n\n// Accessing the __esModule property and exporting $$typeof are required here.\n// The __esModule getter forces the proxy target to create the default export\n// and the $$typeof value is for rendering logic to determine if the module\n// is a client boundary.\nconst { __esModule, $$typeof } = proxy;\nconst __default__ = proxy.default;\n\nconst e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx#Breadcrumb`);\n\nconst e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/breadcrumb.tsx#PageHeader`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/breadcrumb.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/index.ts":
/*!****************************************!*\
  !*** ./src/components/layout/index.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppLayout: () => (/* reexport safe */ _app_layout__WEBPACK_IMPORTED_MODULE_0__.AppLayout),\n/* harmony export */   Breadcrumb: () => (/* reexport safe */ _breadcrumb__WEBPACK_IMPORTED_MODULE_1__.Breadcrumb),\n/* harmony export */   PageHeader: () => (/* reexport safe */ _breadcrumb__WEBPACK_IMPORTED_MODULE_1__.PageHeader)\n/* harmony export */ });\n/* harmony import */ var _app_layout__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app-layout */ \"(rsc)/./src/components/layout/app-layout.tsx\");\n/* harmony import */ var _breadcrumb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./breadcrumb */ \"(rsc)/./src/components/layout/breadcrumb.tsx\");\n/**\n * 布局组件导出\n * 统一导出所有布局相关的组件\n */ \n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTs7O0NBR0MsR0FFdUM7QUFDYSIsInNvdXJjZXMiOlsid2VicGFjazovL2V4cGVyaW1lbnQtbWFuYWdlci1mcm9udGVuZC8uL3NyYy9jb21wb25lbnRzL2xheW91dC9pbmRleC50cz9hMTQ1Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog5biD5bGA57uE5Lu25a+85Ye6XG4gKiDnu5/kuIDlr7zlh7rmiYDmnInluIPlsYDnm7jlhbPnmoTnu4Tku7ZcbiAqL1xuXG5leHBvcnQgeyBBcHBMYXlvdXQgfSBmcm9tICcuL2FwcC1sYXlvdXQnXG5leHBvcnQgeyBCcmVhZGNydW1iLCBQYWdlSGVhZGVyIH0gZnJvbSAnLi9icmVhZGNydW1iJ1xuIl0sIm5hbWVzIjpbIkFwcExheW91dCIsIkJyZWFkY3J1bWIiLCJQYWdlSGVhZGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/index.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
/**
 * 现代化实验管理系统首页
 * 展示系统概览和快速操作
 */

'use client'

import React from 'react'
import Link from 'next/link'
import {
  FlaskConical,
  Plus,
  TrendingUp,
  Activity,
  Clock,
  CheckCircle,
  ArrowRight,
  Zap,
  BarChart3
} from 'lucide-react'

export default function Home() {
  return (
    <div className="flex flex-col gap-8">
      {/* 现代化欢迎横幅 - 与现有页面风格一致 */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 p-8 text-center text-white shadow-2xl">
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent pointer-events-none"></div>
        <div className="relative z-10">
          <h2 className="text-4xl font-bold mb-4">
            🎉 现代化实验管理系统
          </h2>
          <p className="text-xl opacity-90">
            全新的现代化界面已激活，享受更优雅的科研管理体验
          </p>
        </div>
      </div>

      {/* 欢迎区域 - 与现有页面风格一致 */}
      <div className="text-center flex flex-col gap-6">
        <div className="relative inline-block self-center">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur-2xl opacity-20"></div>
          <h1 className="relative text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            实验管理系统
          </h1>
        </div>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
          基于"实验即契约"理念的现代化科研管理平台，让每个实验都可追溯、可复现
        </p>

        {/* 快速操作按钮 - 与现有页面风格一致 */}
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4 pt-4">
          <Link href="/experiments/new" className="group">
            <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex items-center gap-2 hover:scale-105 hover:-translate-y-1">
              <Plus className="h-5 w-5" />
              创建新实验
              <ArrowRight className="h-5 w-5" />
            </button>
          </Link>

          <Link href="/experiments" className="group">
            <button className="bg-white text-gray-700 px-8 py-3 text-lg font-semibold rounded-xl border-2 border-gray-300 hover:border-blue-500 hover:shadow-lg transition-all duration-300 flex items-center gap-2 hover:scale-105 hover:-translate-y-1">
              <FlaskConical className="h-5 w-5" />
              查看所有实验
              <ArrowRight className="h-5 w-5" />
            </button>
          </Link>
        </div>
      </div>

      {/* 快速统计卡片 - 与现有页面风格一致 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* 总实验数 */}
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer hover:-translate-y-1">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-600 text-sm font-medium mb-2">
                总实验数
              </p>
              <p className="text-3xl font-bold text-blue-900">
                24
              </p>
            </div>
            <FlaskConical className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        {/* 已完成 */}
        <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 border border-green-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer hover:-translate-y-1">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-600 text-sm font-medium mb-2">
                已完成
              </p>
              <p className="text-3xl font-bold text-green-900">
                18
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>

        {/* 进行中 */}
        <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-6 border border-orange-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer hover:-translate-y-1">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-orange-600 text-sm font-medium mb-2">
                进行中
              </p>
              <p className="text-3xl font-bold text-orange-900">
                4
              </p>
            </div>
            <Activity className="h-8 w-8 text-orange-500" />
          </div>
        </div>

        {/* 成功率 */}
        <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 border border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer hover:-translate-y-1">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-600 text-sm font-medium mb-2">
                成功率
              </p>
              <p className="text-3xl font-bold text-purple-900">
                92%
              </p>
            </div>
            <TrendingUp className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* 功能特色区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 核心功能 */}
        <div className="bg-white/80 backdrop-blur-lg rounded-3xl p-8 border border-gray-200/50 shadow-2xl hover:shadow-3xl transition-all duration-300">
          <div className="flex flex-col gap-6">
            <div className="flex items-center gap-4">
              <div className="p-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg">
                <Zap className="h-8 w-8 text-white" />
              </div>
              <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                核心功能
              </h2>
            </div>

            <div className="flex flex-col gap-5">
              <div className="flex items-start gap-4 p-4 bg-blue-50/50 hover:bg-blue-50/80 rounded-xl transition-colors duration-300">
                <div className="w-3 h-3 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 mt-1.5 flex-shrink-0"></div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">实验全生命周期管理</h3>
                  <p className="text-gray-600 text-sm">从假设到结论的完整追踪，确保每个实验都可追溯</p>
                </div>
              </div>

              <div className="flex items-start gap-4 p-4 bg-purple-50/50 hover:bg-purple-50/80 rounded-xl transition-colors duration-300">
                <div className="w-3 h-3 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 mt-1.5 flex-shrink-0"></div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">智能复盘系统</h3>
                  <p className="text-gray-600 text-sm">三阶段十二步科学复盘流程，深度挖掘实验价值</p>
                </div>
              </div>

              <div className="flex items-start gap-4 p-4 bg-green-50/50 hover:bg-green-50/80 rounded-xl transition-colors duration-300">
                <div className="w-3 h-3 rounded-full bg-gradient-to-r from-green-500 to-green-600 mt-1.5 flex-shrink-0"></div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">数据可视化分析</h3>
                  <p className="text-gray-600 text-sm">直观的图表和趋势分析，让数据说话</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 最近活动 */}
        <div className="bg-white/80 backdrop-blur-lg rounded-3xl p-8 border border-gray-200/50 shadow-2xl hover:shadow-3xl transition-all duration-300">
          <div className="flex flex-col gap-6">
            <div className="flex items-center gap-4">
              <div className="p-4 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl shadow-lg">
                <BarChart3 className="h-8 w-8 text-white" />
              </div>
              <h2 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                最近活动
              </h2>
            </div>

            <div className="flex flex-col gap-4">
              <div className="flex items-center gap-4 p-5 bg-green-50/80 hover:bg-green-50 rounded-xl border border-green-200/50 hover:shadow-md transition-all duration-300">
                <div className="p-2 bg-green-500 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-white" />
                </div>
                <div className="flex-1">
                  <p className="font-semibold text-gray-900 mb-1">深度学习模型训练</p>
                  <p className="text-sm text-green-600 font-medium">2小时前完成</p>
                </div>
              </div>

              <div className="flex items-center gap-4 p-5 bg-blue-50/80 hover:bg-blue-50 rounded-xl border border-blue-200/50 hover:shadow-md transition-all duration-300">
                <div className="p-2 bg-blue-500 rounded-lg">
                  <Activity className="h-5 w-5 text-white" />
                </div>
                <div className="flex-1">
                  <p className="font-semibold text-gray-900 mb-1">数据预处理实验</p>
                  <p className="text-sm text-blue-600 font-medium">正在进行中</p>
                </div>
              </div>

              <div className="flex items-center gap-4 p-5 bg-orange-50/80 hover:bg-orange-50 rounded-xl border border-orange-200/50 hover:shadow-md transition-all duration-300">
                <div className="p-2 bg-orange-500 rounded-lg">
                  <Clock className="h-5 w-5 text-white" />
                </div>
                <div className="flex-1">
                  <p className="font-semibold text-gray-900 mb-1">模型评估分析</p>
                  <p className="text-sm text-orange-600 font-medium">等待开始</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

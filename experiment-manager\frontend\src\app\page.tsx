/**
 * 现代化实验管理系统首页 - 全新升级版
 * 展示系统概览和快速操作，丰富的视觉效果
 */

'use client'

import React from 'react'
import Link from 'next/link'
import {
  FlaskConical,
  Plus,
  TrendingUp,
  Activity,
  Clock,
  CheckCircle,
  ArrowRight,
  Zap,
  BarChart3,
  Rocket,
  ChevronRight,
  Heart,
  Eye,
  FileText,
  Download,
  Settings,
  Users,
  Target,
  Award,
  Sparkles
} from 'lucide-react'

export default function Home() {
  return (
    <div className="flex flex-col gap-8">
      {/* 现代化欢迎横幅 */}
      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 p-8 text-center text-white shadow-2xl">
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent pointer-events-none"></div>
        <div className="relative z-10">
          <h2 className="text-4xl font-bold mb-4">
            🎉 现代化实验管理系统
          </h2>
          <p className="text-xl opacity-90">
            全新的现代化界面已激活，享受更优雅的科研管理体验
          </p>
        </div>
      </div>

      {/* 欢迎区域 */}
      <div className="text-center flex flex-col gap-6">
        <div className="relative inline-block self-center">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur-2xl opacity-20"></div>
          <h1 className="relative text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            实验管理系统
          </h1>
        </div>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
          基于"实验即契约"理念的现代化科研管理平台，让每个实验都可追溯、可复现
        </p>

        {/* 快速操作按钮 */}
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4 pt-4">
          <Link href="/experiments/new" className="group">
            <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex items-center gap-2 hover:scale-105 hover:-translate-y-1">
              <Plus className="h-5 w-5" />
              创建新实验
              <ArrowRight className="h-5 w-5" />
            </button>
          </Link>

          <Link href="/experiments" className="group">
            <button className="bg-white text-gray-700 px-8 py-3 text-lg font-semibold rounded-xl border-2 border-gray-300 hover:border-blue-500 hover:shadow-lg transition-all duration-300 flex items-center gap-2 hover:scale-105 hover:-translate-y-1">
              <FlaskConical className="h-5 w-5" />
              查看所有实验
              <ArrowRight className="h-5 w-5" />
            </button>
          </Link>
        </div>
      </div>

      {/* 快速统计卡片 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* 总实验数 */}
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer hover:-translate-y-1">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-600 text-sm font-medium mb-2">
                总实验数
              </p>
              <p className="text-3xl font-bold text-blue-900">
                24
              </p>
            </div>
            <FlaskConical className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        {/* 已完成 */}
        <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 border border-green-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer hover:-translate-y-1">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-600 text-sm font-medium mb-2">
                已完成
              </p>
              <p className="text-3xl font-bold text-green-900">
                18
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>

        {/* 进行中 */}
        <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-6 border border-orange-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer hover:-translate-y-1">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-orange-600 text-sm font-medium mb-2">
                进行中
              </p>
              <p className="text-3xl font-bold text-orange-900">
                4
              </p>
            </div>
            <Activity className="h-8 w-8 text-orange-500" />
          </div>
        </div>

        {/* 成功率 */}
        <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 border border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer hover:-translate-y-1">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-600 text-sm font-medium mb-2">
                成功率
              </p>
              <p className="text-3xl font-bold text-purple-900">
                92%
              </p>
            </div>
            <TrendingUp className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* 功能特色区域 - 全面升级 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 核心功能 - 重新设计 */}
        <div className="lg:col-span-2 bg-gradient-to-br from-white via-blue-50/30 to-purple-50/30 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-500 group">
          <div className="flex flex-col gap-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl blur-lg opacity-30 group-hover:opacity-50 transition-opacity duration-300"></div>
                  <div className="relative p-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg">
                    <Zap className="h-8 w-8 text-white" />
                  </div>
                </div>
                <div>
                  <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    核心功能
                  </h2>
                  <p className="text-gray-600 mt-1">强大的实验管理能力</p>
                </div>
              </div>
              <div className="hidden lg:block text-6xl opacity-10 group-hover:opacity-20 transition-opacity duration-300">
                🧪
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="group/item bg-gradient-to-br from-blue-50 to-blue-100/50 hover:from-blue-100 hover:to-blue-200/50 rounded-2xl p-6 border border-blue-200/50 hover:border-blue-300/50 transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                <div className="flex items-start gap-4">
                  <div className="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-md group-hover/item:shadow-lg transition-shadow duration-300">
                    <FlaskConical className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-bold text-gray-900 mb-2 text-lg">实验全生命周期管理</h3>
                    <p className="text-gray-600 text-sm leading-relaxed">从假设提出到结论验证的完整追踪，确保每个实验都可追溯、可复现</p>
                    <div className="mt-3 flex items-center gap-2 text-blue-600 text-sm font-medium">
                      <span>了解更多</span>
                      <ArrowRight className="h-4 w-4 group-hover/item:translate-x-1 transition-transform duration-300" />
                    </div>
                  </div>
                </div>
              </div>

              <div className="group/item bg-gradient-to-br from-purple-50 to-purple-100/50 hover:from-purple-100 hover:to-purple-200/50 rounded-2xl p-6 border border-purple-200/50 hover:border-purple-300/50 transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                <div className="flex items-start gap-4">
                  <div className="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-md group-hover/item:shadow-lg transition-shadow duration-300">
                    <TrendingUp className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-bold text-gray-900 mb-2 text-lg">智能复盘系统</h3>
                    <p className="text-gray-600 text-sm leading-relaxed">三阶段十二步科学复盘流程，深度挖掘实验价值，提升研究效率</p>
                    <div className="mt-3 flex items-center gap-2 text-purple-600 text-sm font-medium">
                      <span>了解更多</span>
                      <ArrowRight className="h-4 w-4 group-hover/item:translate-x-1 transition-transform duration-300" />
                    </div>
                  </div>
                </div>
              </div>

              <div className="group/item bg-gradient-to-br from-green-50 to-green-100/50 hover:from-green-100 hover:to-green-200/50 rounded-2xl p-6 border border-green-200/50 hover:border-green-300/50 transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                <div className="flex items-start gap-4">
                  <div className="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-md group-hover/item:shadow-lg transition-shadow duration-300">
                    <BarChart3 className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-bold text-gray-900 mb-2 text-lg">数据可视化分析</h3>
                    <p className="text-gray-600 text-sm leading-relaxed">直观的图表和趋势分析，让数据说话，洞察实验规律</p>
                    <div className="mt-3 flex items-center gap-2 text-green-600 text-sm font-medium">
                      <span>了解更多</span>
                      <ArrowRight className="h-4 w-4 group-hover/item:translate-x-1 transition-transform duration-300" />
                    </div>
                  </div>
                </div>
              </div>

              <div className="group/item bg-gradient-to-br from-orange-50 to-orange-100/50 hover:from-orange-100 hover:to-orange-200/50 rounded-2xl p-6 border border-orange-200/50 hover:border-orange-300/50 transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                <div className="flex items-start gap-4">
                  <div className="p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-md group-hover/item:shadow-lg transition-shadow duration-300">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-bold text-gray-900 mb-2 text-lg">协作与分享</h3>
                    <p className="text-gray-600 text-sm leading-relaxed">团队协作功能，实验结果分享，知识沉淀与传承</p>
                    <div className="mt-3 flex items-center gap-2 text-orange-600 text-sm font-medium">
                      <span>了解更多</span>
                      <ArrowRight className="h-4 w-4 group-hover/item:translate-x-1 transition-transform duration-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 最近活动 - 重新设计 */}
        <div className="bg-gradient-to-br from-white via-purple-50/30 to-pink-50/30 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-500 group">
          <div className="flex flex-col gap-6">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl blur-lg opacity-30 group-hover:opacity-50 transition-opacity duration-300"></div>
                <div className="relative p-4 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl shadow-lg">
                  <Activity className="h-8 w-8 text-white" />
                </div>
              </div>
              <div>
                <h2 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  最近活动
                </h2>
                <p className="text-gray-600 text-sm">实时动态追踪</p>
              </div>
            </div>

            <div className="flex flex-col gap-4">
              <div className="group/activity bg-gradient-to-r from-green-50 to-emerald-50 hover:from-green-100 hover:to-emerald-100 rounded-2xl p-5 border border-green-200/50 hover:border-green-300/50 hover:shadow-lg transition-all duration-300">
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <div className="absolute inset-0 bg-green-500 rounded-xl blur-sm opacity-20"></div>
                    <div className="relative p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-md">
                      <CheckCircle className="h-5 w-5 text-white" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <p className="font-bold text-gray-900 mb-1">深度学习模型训练</p>
                    <p className="text-sm text-green-600 font-medium">2小时前完成</p>
                    <div className="mt-2 w-full bg-green-200 rounded-full h-2">
                      <div className="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full w-full"></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="group/activity bg-gradient-to-r from-blue-50 to-cyan-50 hover:from-blue-100 hover:to-cyan-100 rounded-2xl p-5 border border-blue-200/50 hover:border-blue-300/50 hover:shadow-lg transition-all duration-300">
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <div className="absolute inset-0 bg-blue-500 rounded-xl blur-sm opacity-20"></div>
                    <div className="relative p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-md">
                      <Activity className="h-5 w-5 text-white animate-pulse" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <p className="font-bold text-gray-900 mb-1">数据预处理实验</p>
                    <p className="text-sm text-blue-600 font-medium">正在进行中</p>
                    <div className="mt-2 w-full bg-blue-200 rounded-full h-2">
                      <div className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full w-3/4 animate-pulse"></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="group/activity bg-gradient-to-r from-orange-50 to-amber-50 hover:from-orange-100 hover:to-amber-100 rounded-2xl p-5 border border-orange-200/50 hover:border-orange-300/50 hover:shadow-lg transition-all duration-300">
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <div className="absolute inset-0 bg-orange-500 rounded-xl blur-sm opacity-20"></div>
                    <div className="relative p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-md">
                      <Clock className="h-5 w-5 text-white" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <p className="font-bold text-gray-900 mb-1">模型评估分析</p>
                    <p className="text-sm text-orange-600 font-medium">等待开始</p>
                    <div className="mt-2 w-full bg-orange-200 rounded-full h-2">
                      <div className="bg-gradient-to-r from-orange-500 to-orange-600 h-2 rounded-full w-1/4"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-4 text-center">
              <Link href="/experiments" className="inline-flex items-center gap-2 text-purple-600 hover:text-purple-700 font-medium text-sm group/link">
                <span>查看所有活动</span>
                <ArrowRight className="h-4 w-4 group-hover/link:translate-x-1 transition-transform duration-300" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

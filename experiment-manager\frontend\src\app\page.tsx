/**
 * 现代化实验管理系统首页
 * 展示系统概览和快速操作
 */

'use client'

import React from 'react'
import Link from 'next/link'
import {
  FlaskConical,
  Plus,
  TrendingUp,
  Activity,
  Clock,
  CheckCircle,
  ArrowRight,
  Zap,
  BarChart3,
  Users,
  Target,
  Award,
  BookOpen,
  Lightbulb,
  Rocket,
  Star,
  Calendar,
  FileText,
  Settings,
  Database,
  Brain,
  Sparkles,
  ChevronRight,
  Play,
  Download,
  Share2,
  Eye,
  Heart,
  MessageCircle,
  GitBranch,
  Timer,
  Shield,
  Cpu,
  Globe
} from 'lucide-react'

export default function Home() {
  return (
    <div className="flex flex-col gap-8">
      {/* 现代化欢迎横幅 - 与现有页面风格一致 */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 p-8 text-center text-white shadow-2xl">
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent pointer-events-none"></div>
        <div className="relative z-10">
          <h2 className="text-4xl font-bold mb-4">
            🎉 现代化实验管理系统
          </h2>
          <p className="text-xl opacity-90">
            全新的现代化界面已激活，享受更优雅的科研管理体验
          </p>
        </div>
      </div>

      {/* 欢迎区域 - 与现有页面风格一致 */}
      <div className="text-center flex flex-col gap-6">
        <div className="relative inline-block self-center">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur-2xl opacity-20"></div>
          <h1 className="relative text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            实验管理系统
          </h1>
        </div>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
          基于"实验即契约"理念的现代化科研管理平台，让每个实验都可追溯、可复现
        </p>

        {/* 快速操作按钮 - 与现有页面风格一致 */}
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4 pt-4">
          <Link href="/experiments/new" className="group">
            <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex items-center gap-2 hover:scale-105 hover:-translate-y-1">
              <Plus className="h-5 w-5" />
              创建新实验
              <ArrowRight className="h-5 w-5" />
            </button>
          </Link>

          <Link href="/experiments" className="group">
            <button className="bg-white text-gray-700 px-8 py-3 text-lg font-semibold rounded-xl border-2 border-gray-300 hover:border-blue-500 hover:shadow-lg transition-all duration-300 flex items-center gap-2 hover:scale-105 hover:-translate-y-1">
              <FlaskConical className="h-5 w-5" />
              查看所有实验
              <ArrowRight className="h-5 w-5" />
            </button>
          </Link>
        </div>
      </div>

      {/* 快速统计卡片 - 与现有页面风格一致 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* 总实验数 */}
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer hover:-translate-y-1">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-600 text-sm font-medium mb-2">
                总实验数
              </p>
              <p className="text-3xl font-bold text-blue-900">
                24
              </p>
            </div>
            <FlaskConical className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        {/* 已完成 */}
        <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 border border-green-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer hover:-translate-y-1">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-600 text-sm font-medium mb-2">
                已完成
              </p>
              <p className="text-3xl font-bold text-green-900">
                18
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>

        {/* 进行中 */}
        <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-6 border border-orange-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer hover:-translate-y-1">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-orange-600 text-sm font-medium mb-2">
                进行中
              </p>
              <p className="text-3xl font-bold text-orange-900">
                4
              </p>
            </div>
            <Activity className="h-8 w-8 text-orange-500" />
          </div>
        </div>

        {/* 成功率 */}
        <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 border border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer hover:-translate-y-1">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-600 text-sm font-medium mb-2">
                成功率
              </p>
              <p className="text-3xl font-bold text-purple-900">
                92%
              </p>
            </div>
            <TrendingUp className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* 热门实验展示 */}
      <div className="bg-white/80 backdrop-blur-lg rounded-3xl p-8 border border-gray-200/50 shadow-2xl">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <div className="p-4 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl shadow-lg">
              <Rocket className="h-8 w-8 text-white" />
            </div>
            <div>
              <h2 className="text-3xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                🔥 热门实验
              </h2>
              <p className="text-gray-600">最受关注的实验项目</p>
            </div>
          </div>
          <Link href="/experiments" className="flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium transition-colors">
            查看全部 <ChevronRight className="h-4 w-4" />
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* 热门实验卡片 */}
          {[
            {
              title: "深度学习图像识别",
              description: "基于CNN的高精度图像分类模型",
              status: "进行中",
              progress: 75,
              likes: 128,
              views: 1240,
              tags: ["深度学习", "计算机视觉"],
              color: "blue"
            },
            {
              title: "自然语言处理优化",
              description: "BERT模型在中文文本分析中的应用",
              status: "已完成",
              progress: 100,
              likes: 95,
              views: 890,
              tags: ["NLP", "BERT"],
              color: "green"
            },
            {
              title: "强化学习游戏AI",
              description: "使用DQN训练智能游戏代理",
              status: "计划中",
              progress: 25,
              likes: 67,
              views: 456,
              tags: ["强化学习", "游戏AI"],
              color: "purple"
            }
          ].map((experiment, index) => (
            <div key={index} className="group bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-6 border border-gray-200/50 hover:border-gray-300/50 hover:shadow-xl transition-all duration-300 cursor-pointer">
              <div className="flex items-start justify-between mb-4">
                <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                  experiment.status === '已完成' ? 'bg-green-100 text-green-700' :
                  experiment.status === '进行中' ? 'bg-blue-100 text-blue-700' :
                  'bg-orange-100 text-orange-700'
                }`}>
                  {experiment.status}
                </div>
                <div className="flex items-center gap-3 text-gray-500 text-sm">
                  <div className="flex items-center gap-1">
                    <Heart className="h-4 w-4" />
                    {experiment.likes}
                  </div>
                  <div className="flex items-center gap-1">
                    <Eye className="h-4 w-4" />
                    {experiment.views}
                  </div>
                </div>
              </div>

              <h3 className="font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                {experiment.title}
              </h3>
              <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                {experiment.description}
              </p>

              <div className="mb-4">
                <div className="flex items-center justify-between text-sm mb-2">
                  <span className="text-gray-600">进度</span>
                  <span className="font-medium">{experiment.progress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full bg-gradient-to-r ${
                      experiment.color === 'blue' ? 'from-blue-500 to-blue-600' :
                      experiment.color === 'green' ? 'from-green-500 to-green-600' :
                      'from-purple-500 to-purple-600'
                    }`}
                    style={{ width: `${experiment.progress}%` }}
                  ></div>
                </div>
              </div>

              <div className="flex flex-wrap gap-2">
                {experiment.tags.map((tag, tagIndex) => (
                  <span key={tagIndex} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-lg">
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 数据洞察仪表板 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 实验趋势图表 */}
        <div className="lg:col-span-2 bg-white/80 backdrop-blur-lg rounded-3xl p-8 border border-gray-200/50 shadow-2xl">
          <div className="flex items-center gap-4 mb-6">
            <div className="p-4 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl shadow-lg">
              <BarChart3 className="h-8 w-8 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                📊 实验趋势分析
              </h2>
              <p className="text-gray-600">过去30天的实验活动统计</p>
            </div>
          </div>

          {/* 模拟图表区域 */}
          <div className="h-64 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl p-6 flex items-center justify-center border border-indigo-100">
            <div className="text-center">
              <BarChart3 className="h-16 w-16 text-indigo-400 mx-auto mb-4" />
              <p className="text-indigo-600 font-medium">实验活动趋势图表</p>
              <p className="text-gray-500 text-sm mt-2">显示实验创建、完成和成功率变化</p>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4 mt-6">
            <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl">
              <div className="text-2xl font-bold text-blue-600">+23%</div>
              <div className="text-sm text-blue-700">本月增长</div>
            </div>
            <div className="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-xl">
              <div className="text-2xl font-bold text-green-600">94.2%</div>
              <div className="text-sm text-green-700">成功率</div>
            </div>
            <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl">
              <div className="text-2xl font-bold text-purple-600">156h</div>
              <div className="text-sm text-purple-700">总时长</div>
            </div>
          </div>
        </div>

        {/* 快速操作面板 */}
        <div className="bg-white/80 backdrop-blur-lg rounded-3xl p-8 border border-gray-200/50 shadow-2xl">
          <div className="flex items-center gap-4 mb-6">
            <div className="p-4 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl shadow-lg">
              <Zap className="h-8 w-8 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                ⚡ 快速操作
              </h2>
            </div>
          </div>

          <div className="space-y-4">
            {[
              { icon: Plus, label: "新建实验", desc: "创建新的实验项目", color: "blue" },
              { icon: FileText, label: "导入数据", desc: "批量导入实验数据", color: "green" },
              { icon: Download, label: "导出报告", desc: "生成实验分析报告", color: "purple" },
              { icon: Settings, label: "系统设置", desc: "配置系统参数", color: "orange" },
              { icon: Users, label: "团队管理", desc: "管理团队成员", color: "pink" }
            ].map((action, index) => (
              <button key={index} className="w-full flex items-center gap-4 p-4 bg-gradient-to-r from-gray-50 to-white rounded-xl border border-gray-200/50 hover:border-gray-300/50 hover:shadow-md transition-all duration-300 group">
                <div className={`p-2 rounded-lg ${
                  action.color === 'blue' ? 'bg-blue-100 text-blue-600' :
                  action.color === 'green' ? 'bg-green-100 text-green-600' :
                  action.color === 'purple' ? 'bg-purple-100 text-purple-600' :
                  action.color === 'orange' ? 'bg-orange-100 text-orange-600' :
                  'bg-pink-100 text-pink-600'
                }`}>
                  <action.icon className="h-5 w-5" />
                </div>
                <div className="flex-1 text-left">
                  <div className="font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                    {action.label}
                  </div>
                  <div className="text-sm text-gray-500">{action.desc}</div>
                </div>
                <ChevronRight className="h-4 w-4 text-gray-400 group-hover:text-blue-500 transition-colors" />
              </button>
            ))}
          </div>
        </div>
      </div>
